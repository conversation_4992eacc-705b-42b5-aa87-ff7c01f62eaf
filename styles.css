/* Import Futura Font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Futura', 'Inter', sans-serif;
    line-height: 1.6;
    color: #000000;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }

p {
    margin-bottom: 1rem;
    color: #666;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.btn-primary {
    background: #8AA49D;
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(138, 164, 157, 0.3);
    background: #7a9490;
}

.btn-secondary {
    background: transparent;
    color: #8AA49D;
    border: 2px solid #8AA49D;
}

.btn-secondary:hover {
    background: #8AA49D;
    color: white;
}

.btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo img {
    height: 40px;
    width: auto;
    max-width: 150px;
    object-fit: contain;
}

.nav-logo h2 {
    color: #8AA49D;
    margin: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #8AA49D;
}

.cta-nav {
    background: #8AA49D;
    color: white !important;
    padding: 8px 16px;
    border-radius: 6px;
}

.cta-nav:hover {
    background: #7a9490;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding-top: 80px;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: #2d3748;
}

.highlight {
    color: #8AA49D;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #4a5568;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat {
    text-align: center;
}

.stat h3 {
    font-size: 2rem;
    color: #8AA49D;
    margin-bottom: 0.5rem;
}

.stat p {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-image {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-graphic {
    position: relative;
    width: 400px;
    height: 400px;
}

.hero-circle {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: #8AA49D;
    opacity: 0.1;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: pulse 3s ease-in-out infinite;
}

.floating-card {
    position: absolute;
    background: white;
    padding: 1rem;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    animation: float 6s ease-in-out infinite;
}

.card-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.card-2 {
    top: 30%;
    right: 10%;
    animation-delay: 1.5s;
}

.card-3 {
    bottom: 30%;
    left: 5%;
    animation-delay: 3s;
}

.card-4 {
    bottom: 20%;
    right: 15%;
    animation-delay: 4.5s;
}

.floating-card i {
    font-size: 1.5rem;
    color: #8AA49D;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    color: #2d3748;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about {
    padding: 6rem 0;
    background: white;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h3 {
    color: #2d3748;
    margin-bottom: 1.5rem;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 2rem;
}

.credentials {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.credential {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.credential i {
    font-size: 1.5rem;
    color: #8AA49D;
    margin-top: 0.25rem;
}

.credential h4 {
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.credential p {
    color: #666;
    margin: 0;
}

.profile-card {
    background: white;
    padding: 2.3rem;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 345px;
    margin: 0 auto;
    transform: scale(1.15);
}

.profile-avatar {
    width: 92px;
    height: 92px;
    border-radius: 50%;
    background: #8AA49D;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    overflow: hidden;
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.profile-avatar i {
    font-size: 2rem;
    color: white;
}

.profile-card h4 {
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-size: 1.15rem;
}

.profile-card p {
    color: #8AA49D;
    margin-bottom: 1.5rem;
    font-size: 1.05rem;
}

.profile-stats {
    display: flex;
    justify-content: space-around;
    border-top: 1px solid #e2e8f0;
    padding-top: 1rem;
}

.profile-stat {
    text-align: center;
}

.profile-stat span {
    display: block;
    font-size: 1.7rem;
    font-weight: 600;
    color: #8AA49D;
}

.profile-stat small {
    color: #666;
    font-size: 0.8rem;
}

/* Services Section */
.services {
    padding: 6rem 0;
    background: #f8fafc;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.service-card {
    background: white;
    padding: 2.5rem;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.service-card.featured {
    border: 2px solid #8AA49D;
    transform: scale(1.05);
}

.service-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: #8AA49D;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.service-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: #8AA49D;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.service-icon i {
    font-size: 1.5rem;
    color: white;
}

.service-card h3 {
    color: #2d3748;
    margin-bottom: 1rem;
}

.service-card p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-features {
    list-style: none;
    margin-bottom: 2rem;
}

.service-features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: #666;
}

.service-features i {
    color: #10b981;
    font-size: 0.8rem;
}

.service-price {
    font-size: 1.25rem;
    font-weight: 600;
    color: #8AA49D;
    text-align: center;
    padding: 1rem;
    background: #f0f8f6;
    border-radius: 8px;
}

/* Animations */
@keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Testimonials Section */
.testimonials {
    padding: 6rem 0;
    background: white;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 16px;
    border-left: 4px solid #8AA49D;
}

.testimonial-content {
    margin-bottom: 1.5rem;
}

.stars {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.stars i {
    color: #fbbf24;
}

.testimonial-content p {
    font-style: italic;
    color: #4a5568;
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #8AA49D;
    display: flex;
    align-items: center;
    justify-content: center;
}

.author-avatar i {
    color: white;
    font-size: 1.2rem;
}

.author-info h4 {
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.author-info p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

/* Contact Section */
.contact {
    padding: 6rem 0;
    background: #f8fafc;
}

.contact-content {
    display: grid;
    grid-template-columns: 1.2fr 0.8fr;
    gap: 4rem;
}

.contact-info h3 {
    color: #2d3748;
    margin-bottom: 2rem;
}

.contact-item-row {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
}

.contact-item i {
    font-size: 1.5rem;
    color: #8AA49D;
    margin-top: 0.25rem;
}

.contact-item h4 {
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.contact-item p {
    color: #666;
    margin: 0;
}

.social-links {
    margin-top: 2rem;
}

.social-links h4 {
    color: #2d3748;
    margin-bottom: 1rem;
}

.social-icons {
    display: flex;
    gap: 1rem;
}

.social-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #8AA49D;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.social-icon:hover {
    background: #7a9490;
}

.social-icon:hover {
    transform: translateY(-2px);
}

.contact-form {
    background: white;
    padding: 2.5rem;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-row {
    display: flex;
    gap: 4%;
    margin-bottom: 1.5rem;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #2d3748;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #8AA49D;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Booking Section */
.booking {
    padding: 6rem 0;
    background: #8AA49D;
    color: white;
}

.booking-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.booking-header {
    margin-bottom: 3rem;
}

.booking-header h2 {
    color: white;
    margin-bottom: 1rem;
}

.booking-header p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
}

.booking-form {
    background: white;
    padding: 2.5rem;
    border-radius: 16px;
    color: #333;
}



.booking-note {
    margin-top: 1rem;
    color: #666;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.booking-note i {
    color: #8AA49D;
}

/* Footer */
.footer {
    background: #2d3748;
    color: white;
    padding: 2rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.footer-section img {
    height: 40px;
    width: auto;
    max-width: 150px;
    object-fit: contain;
    margin-bottom: 1rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 0.75rem;
    color: white;
}

.footer-section p {
    color: #a0aec0;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #a0aec0;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: white;
}

.footer-brand-row {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 1rem;
}

.footer-brand-row .footer-slogan {
    flex: 1;
    margin-bottom: 1.5rem;
}

.footer-social {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.footer-contact p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: #a0aec0;
}

.footer-contact i {
    color: #8AA49D;
}

.footer-bottom {
    border-top: 1px solid #4a5568;
    padding-top: 1rem;
    text-align: center;
}

.footer-bottom p {
    color: #a0aec0;
    margin: 0;
}

/* Language Selector */
.language-selector {
    margin-top: 1.5rem;
}

.language-selector h4 {
    color: white;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.language-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.lang-btn {
    background: transparent;
    border: 2px solid #8AA49D;
    color: #a0aec0;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.lang-btn:hover {
    background: #8AA49D;
    color: white;
    transform: translateY(-1px);
}

.lang-btn.active {
    background: #8AA49D;
    color: white;
    border-color: #8AA49D;
}

.lang-btn i {
    font-size: 0.8rem;
}

/* Mobile Navigation */
@media (max-width: 768px) {
    .nav-logo img {
        height: 35px;
        max-width: 120px;
    }

    .footer-section img {
        height: 35px;
        max-width: 120px;
    }

    .profile-avatar {
        width: 70px;
        height: 70px;
    }

    .profile-card {
        transform: scale(1.05);
        max-width: 280px;
        padding: 1.8rem;
    }

    .profile-card h4 {
        font-size: 1.1rem;
    }

    .profile-card p {
        font-size: 1rem;
    }

    .profile-stat span {
        font-size: 1.4rem;
    }
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        justify-content: center;
        gap: 1rem;
    }

    .hero-graphic {
        width: 300px;
        height: 300px;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-item-row {
        flex-direction: row;
        align-items: flex-start;
        gap: 2rem;
    }

    .contact-item-row .contact-item,
    .contact-item-row .social-links {
        flex: 1;
        margin-bottom: 0;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .service-card.featured {
        transform: none;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .floating-card {
        font-size: 0.8rem;
        padding: 0.75rem;
    }

    .floating-card i {
        font-size: 1.2rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr 1fr;
        text-align: left;
        gap: 1rem;
    }

    /* Footer brand row mobilde */
    .footer-brand-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .footer-brand-row .footer-slogan {
        margin-bottom: 0.5rem;
    }

    .footer-brand-row .footer-social {
        margin-top: 0;
        align-self: flex-start;
    }

    /* Services ve Company yan yana */
    .footer-content .footer-section:nth-child(2),
    .footer-content .footer-section:nth-child(3) {
        grid-column: span 1;
    }

    /* Contact Info ortala ve tam genişlik */
    .footer-content .footer-section:nth-child(4) {
        grid-column: 1 / -1;
        text-align: center;
        margin-top: 1rem;
    }

    /* Form responsive design */
    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .form-row .form-group {
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .service-card,
    .contact-form,
    .booking-form {
        padding: 1.5rem;
    }

    .container {
        padding: 0 15px;
    }
}
