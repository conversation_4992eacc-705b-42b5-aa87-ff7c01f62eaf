<!DOCTYPE html>
<html lang="en" id="admin-html-root">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-en="WANDOH Admin Panel" data-tr="WANDOH Yönetim Paneli">WANDOH Admin Panel</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="admin-styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="login-screen">
        <div class="login-container">
            <div class="login-header">
                <img src="2.png" alt="WANDOH Logo" style="height: 60px; margin-bottom: 1rem;">
                <h2 data-en="Admin Panel" data-tr="Yönetim Paneli">Admin Panel</h2>
                <p data-en="Please enter your password to access the admin panel" data-tr="Yönetim paneline erişmek için şifrenizi girin">Please enter your password to access the admin panel</p>
            </div>
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="password" data-en="Password" data-tr="Şifre">Password</label>
                    <div class="password-input-container">
                        <input type="password" id="password" name="password" required>
                        <button type="button" class="password-toggle" id="passwordToggle">
                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                        </button>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i>
                    <span data-en="Login" data-tr="Giriş Yap">Login</span>
                </button>
                <div id="loginError" class="login-error" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span data-en="Invalid password. Please try again." data-tr="Geçersiz şifre. Lütfen tekrar deneyin.">Invalid password. Please try again.</span>
                </div>
            </form>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div id="adminDashboard" class="admin-dashboard" style="display: none;">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-content">
                <div class="header-left">
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <img src="2.png" alt="WANDOH Logo" style="height: 35px;">
                        <span data-en="Admin Panel" data-tr="Yönetim Paneli">Admin Panel</span>
                    </div>
                    <div class="breadcrumb">
                        <i class="fas fa-home"></i>
                        <span data-en="Dashboard" data-tr="Kontrol Paneli">Dashboard</span>
                    </div>
                </div>
                <div class="header-right">
                    <div class="language-selector">
                        <button class="lang-btn active" data-lang="en">
                            <i class="fas fa-globe"></i> EN
                        </button>
                        <button class="lang-btn" data-lang="tr">
                            <i class="fas fa-globe"></i> TR
                        </button>
                    </div>
                    <button id="logoutBtn" class="btn btn-secondary">
                        <i class="fas fa-sign-out-alt"></i>
                        <span data-en="Logout" data-tr="Çıkış">Logout</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="admin-nav">
            <div class="nav-content">
                <button class="nav-btn active" data-section="overview">
                    <i class="fas fa-chart-bar"></i>
                    <span data-en="Overview" data-tr="Genel Bakış">Overview</span>
                </button>
                <button class="nav-btn" data-section="contacts">
                    <i class="fas fa-envelope"></i>
                    <span data-en="Contact Forms" data-tr="İletişim Formları">Contact Forms</span>
                    <span class="notification-badge" id="contactBadge">0</span>
                </button>
                <button class="nav-btn" data-section="bookings">
                    <i class="fas fa-calendar-alt"></i>
                    <span data-en="Booking Requests" data-tr="Randevu Talepleri">Booking Requests</span>
                    <span class="notification-badge" id="bookingBadge">0</span>
                </button>
                <button class="nav-btn" data-section="settings">
                    <i class="fas fa-cog"></i>
                    <span data-en="Settings" data-tr="Ayarlar">Settings</span>
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Overview Section -->
            <section id="overviewSection" class="admin-section active">
                <div class="section-header">
                    <h2 data-en="Dashboard Overview" data-tr="Kontrol Paneli Genel Bakış">Dashboard Overview</h2>
                    <p data-en="Summary of your recent activity and submissions" data-tr="Son aktiviteleriniz ve başvurularınızın özeti">Summary of your recent activity and submissions</p>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalContacts">0</h3>
                            <p data-en="Total Contact Forms" data-tr="Toplam İletişim Formu">Total Contact Forms</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalBookings">0</h3>
                            <p data-en="Total Booking Requests" data-tr="Toplam Randevu Talebi">Total Booking Requests</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="unreadCount">0</h3>
                            <p data-en="Unread Items" data-tr="Okunmamış Öğeler">Unread Items</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="todayCount">0</h3>
                            <p data-en="Today's Submissions" data-tr="Bugünkü Başvurular">Today's Submissions</p>
                        </div>
                    </div>
                </div>

                <div class="recent-activity">
                    <h3 data-en="Recent Activity" data-tr="Son Aktiviteler">Recent Activity</h3>
                    <div id="recentActivityList" class="activity-list">
                        <!-- Recent items will be populated here -->
                    </div>
                </div>
            </section>

            <!-- Contact Forms Section -->
            <section id="contactsSection" class="admin-section">
                <div class="section-header">
                    <h2 data-en="Contact Form Submissions" data-tr="İletişim Formu Başvuruları">Contact Form Submissions</h2>
                    <div class="section-actions">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="contactSearch" placeholder="Search contacts..." data-en-placeholder="Search contacts..." data-tr-placeholder="İletişimleri ara...">
                        </div>
                        <select id="contactFilter" class="filter-select">
                            <option value="all" data-en="All Status" data-tr="Tüm Durumlar">All Status</option>
                            <option value="unread" data-en="Unread" data-tr="Okunmamış">Unread</option>
                            <option value="read" data-en="Read" data-tr="Okunmuş">Read</option>
                            <option value="processed" data-en="Processed" data-tr="İşlenmiş">Processed</option>
                        </select>
                        <button id="exportContactsBtn" class="btn btn-secondary">
                            <i class="fas fa-download"></i>
                            <span data-en="Export" data-tr="Dışa Aktar">Export</span>
                        </button>
                    </div>
                </div>
                <div id="contactsList" class="submissions-list">
                    <!-- Contact submissions will be populated here -->
                </div>
            </section>

            <!-- Booking Requests Section -->
            <section id="bookingsSection" class="admin-section">
                <div class="section-header">
                    <h2 data-en="Consultation Booking Requests" data-tr="Danışmanlık Randevu Talepleri">Consultation Booking Requests</h2>
                    <div class="section-actions">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="bookingSearch" placeholder="Search bookings..." data-en-placeholder="Search bookings..." data-tr-placeholder="Randevuları ara...">
                        </div>
                        <select id="bookingFilter" class="filter-select">
                            <option value="all" data-en="All Status" data-tr="Tüm Durumlar">All Status</option>
                            <option value="unread" data-en="Unread" data-tr="Okunmamış">Unread</option>
                            <option value="read" data-en="Read" data-tr="Okunmuş">Read</option>
                            <option value="processed" data-en="Processed" data-tr="İşlenmiş">Processed</option>
                        </select>
                        <button id="exportBookingsBtn" class="btn btn-secondary">
                            <i class="fas fa-download"></i>
                            <span data-en="Export" data-tr="Dışa Aktar">Export</span>
                        </button>
                    </div>
                </div>
                <div id="bookingsList" class="submissions-list">
                    <!-- Booking submissions will be populated here -->
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settingsSection" class="admin-section">
                <div class="section-header">
                    <h2 data-en="Settings" data-tr="Ayarlar">Settings</h2>
                    <p data-en="Manage your admin panel preferences" data-tr="Yönetim paneli tercihlerinizi yönetin">Manage your admin panel preferences</p>
                </div>
                
                <div class="settings-grid">
                    <div class="setting-card">
                        <h3 data-en="Password Management" data-tr="Şifre Yönetimi">Password Management</h3>
                        <p data-en="Change your admin panel password" data-tr="Yönetim paneli şifrenizi değiştirin">Change your admin panel password</p>
                        <button id="changePasswordBtn" class="btn btn-primary">
                            <i class="fas fa-key"></i>
                            <span data-en="Change Password" data-tr="Şifre Değiştir">Change Password</span>
                        </button>
                    </div>
                    
                    <div class="setting-card">
                        <h3 data-en="Data Management" data-tr="Veri Yönetimi">Data Management</h3>
                        <p data-en="Export or clear all stored data" data-tr="Tüm saklanan verileri dışa aktarın veya temizleyin">Export or clear all stored data</p>
                        <div class="setting-actions">
                            <button id="exportAllBtn" class="btn btn-secondary">
                                <i class="fas fa-download"></i>
                                <span data-en="Export All Data" data-tr="Tüm Verileri Dışa Aktar">Export All Data</span>
                            </button>
                            <button id="clearDataBtn" class="btn btn-danger">
                                <i class="fas fa-trash"></i>
                                <span data-en="Clear All Data" data-tr="Tüm Verileri Temizle">Clear All Data</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Modals -->
    <div id="submissionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Submission Details</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Submission details will be populated here -->
            </div>
            <div class="modal-footer">
                <button id="markAsReadBtn" class="btn btn-primary">Mark as Read</button>
                <button id="markAsProcessedBtn" class="btn btn-success">Mark as Processed</button>
                <button id="deleteSubmissionBtn" class="btn btn-danger">Delete</button>
                <button class="btn btn-secondary modal-close">Close</button>
            </div>
        </div>
    </div>

    <script src="admin-script.js"></script>
</body>
</html>
