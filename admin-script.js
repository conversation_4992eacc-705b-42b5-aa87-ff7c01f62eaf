// Admin Panel JavaScript
class AdminPanel {
    constructor() {
        this.adminPassword = 'M4qsxWandoh7t.';
        this.currentLanguage = localStorage.getItem('adminLanguage') || 'en';
        this.currentSection = 'overview';
        this.currentSubmission = null;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupLanguage();
        this.checkAuthentication();
    }

    setupEventListeners() {
        console.log('Setting up event listeners...');

        // Login form - setup immediately since we're already in DOMContentLoaded
        const loginForm = document.getElementById('loginForm');
        console.log('Login form found:', !!loginForm);

        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                console.log('Form submitted');
                e.preventDefault();
                this.handleLogin();
            });

            // Also add click event to login button as backup
            const loginButton = loginForm.querySelector('button[type="submit"]');
            if (loginButton) {
                loginButton.addEventListener('click', (e) => {
                    console.log('Login button clicked');
                    e.preventDefault();
                    this.handleLogin();
                });
            }

            // Add Enter key support for password input
            const passwordInput = document.getElementById('password');
            if (passwordInput) {
                passwordInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        console.log('Enter key pressed in password field');
                        e.preventDefault();
                        this.handleLogin();
                    }
                });
            }
        }

        // Password toggle - setup immediately
        const passwordToggle = document.getElementById('passwordToggle');
        console.log('Password toggle found:', !!passwordToggle);

        if (passwordToggle) {
            passwordToggle.addEventListener('click', (e) => {
                console.log('Password toggle clicked');
                e.preventDefault();
                this.togglePasswordVisibility();
            });
        }

        // Setup other event listeners
        this.setupDashboardEventListeners();
    }

    setupDashboardEventListeners() {
        // Logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.logout();
            });
        }

        // Navigation buttons
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const section = e.currentTarget.getAttribute('data-section');
                this.showSection(section);
            });
        });

        // Language buttons
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const lang = e.currentTarget.getAttribute('data-lang');
                this.setLanguage(lang);
            });
        });

        // Search and filter
        document.getElementById('contactSearch').addEventListener('input', () => {
            this.filterSubmissions('contact');
        });

        document.getElementById('bookingSearch').addEventListener('input', () => {
            this.filterSubmissions('booking');
        });

        document.getElementById('contactFilter').addEventListener('change', () => {
            this.filterSubmissions('contact');
        });

        document.getElementById('bookingFilter').addEventListener('change', () => {
            this.filterSubmissions('booking');
        });

        // Export buttons
        document.getElementById('exportContactsBtn').addEventListener('click', () => {
            this.exportData('contacts');
        });

        document.getElementById('exportBookingsBtn').addEventListener('click', () => {
            this.exportData('bookings');
        });

        document.getElementById('exportAllBtn').addEventListener('click', () => {
            this.exportData('all');
        });

        // Settings buttons
        document.getElementById('changePasswordBtn').addEventListener('click', () => {
            this.changePassword();
        });

        document.getElementById('clearDataBtn').addEventListener('click', () => {
            this.clearAllData();
        });

        // Modal events
        document.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => {
                this.closeModal();
            });
        });

        document.getElementById('markAsReadBtn').addEventListener('click', () => {
            this.updateSubmissionStatus('read');
        });

        document.getElementById('markAsProcessedBtn').addEventListener('click', () => {
            this.updateSubmissionStatus('processed');
        });

        document.getElementById('deleteSubmissionBtn').addEventListener('click', () => {
            this.deleteCurrentSubmission();
        });

        // Close modal when clicking outside
        document.getElementById('submissionModal').addEventListener('click', (e) => {
            if (e.target.id === 'submissionModal') {
                this.closeModal();
            }
        });
    }

    setupLanguage() {
        this.setLanguage(this.currentLanguage);
    }

    setLanguage(lang) {
        this.currentLanguage = lang;
        localStorage.setItem('adminLanguage', lang);
        
        // Update HTML lang attribute
        document.getElementById('admin-html-root').setAttribute('lang', lang);
        
        // Update all elements with data attributes
        const elements = document.querySelectorAll('[data-en][data-tr]');
        elements.forEach(element => {
            const text = element.getAttribute(`data-${lang}`);
            if (text) {
                if (element.tagName === 'TITLE') {
                    element.textContent = text;
                } else if (element.hasAttribute(`data-${lang}-placeholder`)) {
                    element.placeholder = element.getAttribute(`data-${lang}-placeholder`);
                } else {
                    element.textContent = text;
                }
            }
        });

        // Update language buttons
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-lang') === lang) {
                btn.classList.add('active');
            }
        });

        // Update placeholders
        const placeholders = {
            en: {
                contactSearch: 'Search contacts...',
                bookingSearch: 'Search bookings...'
            },
            tr: {
                contactSearch: 'İletişimleri ara...',
                bookingSearch: 'Randevuları ara...'
            }
        };

        document.getElementById('contactSearch').placeholder = placeholders[lang].contactSearch;
        document.getElementById('bookingSearch').placeholder = placeholders[lang].bookingSearch;
    }

    checkAuthentication() {
        const isAuthenticated = sessionStorage.getItem('adminAuthenticated') === 'true';
        if (isAuthenticated) {
            this.showDashboard();
        } else {
            this.showLogin();
        }
    }

    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.getElementById('passwordToggleIcon');

        if (passwordInput && toggleIcon) {
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    }

    handleLogin() {
        console.log('handleLogin called');
        const password = document.getElementById('password').value;
        const errorDiv = document.getElementById('loginError');

        console.log('Entered password:', password);
        console.log('Expected password:', this.adminPassword);
        console.log('Password match:', password === this.adminPassword);

        if (password === this.adminPassword) {
            console.log('Password correct, logging in...');
            sessionStorage.setItem('adminAuthenticated', 'true');
            this.showDashboard();
            errorDiv.style.display = 'none';
        } else {
            console.log('Password incorrect');
            errorDiv.style.display = 'block';
            document.getElementById('password').value = '';
        }
    }

    logout() {
        sessionStorage.removeItem('adminAuthenticated');
        this.showLogin();
    }

    showLogin() {
        document.getElementById('loginScreen').style.display = 'flex';
        document.getElementById('adminDashboard').style.display = 'none';
    }

    showDashboard() {
        document.getElementById('loginScreen').style.display = 'none';
        document.getElementById('adminDashboard').style.display = 'flex';
        this.loadDashboardData();
    }

    showSection(sectionName) {
        // Update navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

        // Update sections
        document.querySelectorAll('.admin-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${sectionName}Section`).classList.add('active');

        this.currentSection = sectionName;

        // Load section-specific data
        if (sectionName === 'contacts') {
            this.loadContactSubmissions();
        } else if (sectionName === 'bookings') {
            this.loadBookingSubmissions();
        } else if (sectionName === 'overview') {
            this.loadOverviewData();
        }
    }

    loadDashboardData() {
        this.updateNotificationBadges();
        this.loadOverviewData();
        this.loadContactSubmissions();
        this.loadBookingSubmissions();
    }

    updateNotificationBadges() {
        const contacts = this.getContactSubmissions();
        const bookings = this.getBookingSubmissions();
        
        const unreadContacts = contacts.filter(item => item.status === 'unread').length;
        const unreadBookings = bookings.filter(item => item.status === 'unread').length;
        
        document.getElementById('contactBadge').textContent = unreadContacts || '';
        document.getElementById('bookingBadge').textContent = unreadBookings || '';
    }

    loadOverviewData() {
        const contacts = this.getContactSubmissions();
        const bookings = this.getBookingSubmissions();
        
        const totalContacts = contacts.length;
        const totalBookings = bookings.length;
        const unreadCount = contacts.filter(item => item.status === 'unread').length + 
                           bookings.filter(item => item.status === 'unread').length;
        
        const today = new Date().toDateString();
        const todayCount = [...contacts, ...bookings].filter(item => 
            new Date(item.timestamp).toDateString() === today
        ).length;
        
        document.getElementById('totalContacts').textContent = totalContacts;
        document.getElementById('totalBookings').textContent = totalBookings;
        document.getElementById('unreadCount').textContent = unreadCount;
        document.getElementById('todayCount').textContent = todayCount;
        
        this.loadRecentActivity();
    }

    loadRecentActivity() {
        const contacts = this.getContactSubmissions();
        const bookings = this.getBookingSubmissions();
        
        const allItems = [...contacts, ...bookings]
            .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
            .slice(0, 5);
        
        const activityList = document.getElementById('recentActivityList');
        
        if (allItems.length === 0) {
            activityList.innerHTML = `
                <div class="activity-item">
                    <div class="activity-content">
                        <p>${this.currentLanguage === 'en' ? 'No recent activity' : 'Son aktivite yok'}</p>
                    </div>
                </div>
            `;
            return;
        }
        
        activityList.innerHTML = allItems.map(item => {
            const isContact = item.type === 'contact';
            const name = isContact ? item.data.name : item.data.name;
            const email = isContact ? item.data.email : item.data.email;
            const timeAgo = this.getTimeAgo(item.timestamp);
            
            return `
                <div class="activity-item" onclick="adminPanel.showSubmissionModal('${item.id}', '${item.type}')">
                    <div class="activity-icon ${item.type}">
                        <i class="fas fa-${isContact ? 'envelope' : 'calendar-alt'}"></i>
                    </div>
                    <div class="activity-content">
                        <h4>${name}</h4>
                        <p>${email} - ${isContact ? (this.currentLanguage === 'en' ? 'Contact Form' : 'İletişim Formu') : (this.currentLanguage === 'en' ? 'Booking Request' : 'Randevu Talebi')}</p>
                    </div>
                    <div class="activity-time">${timeAgo}</div>
                </div>
            `;
        }).join('');
    }

    getContactSubmissions() {
        return JSON.parse(localStorage.getItem('contactSubmissions') || '[]');
    }

    getBookingSubmissions() {
        return JSON.parse(localStorage.getItem('bookingSubmissions') || '[]');
    }

    getTimeAgo(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diffInMinutes = Math.floor((now - time) / (1000 * 60));
        
        if (diffInMinutes < 1) {
            return this.currentLanguage === 'en' ? 'Just now' : 'Şimdi';
        } else if (diffInMinutes < 60) {
            return this.currentLanguage === 'en' ? `${diffInMinutes}m ago` : `${diffInMinutes}d önce`;
        } else if (diffInMinutes < 1440) {
            const hours = Math.floor(diffInMinutes / 60);
            return this.currentLanguage === 'en' ? `${hours}h ago` : `${hours}s önce`;
        } else {
            const days = Math.floor(diffInMinutes / 1440);
            return this.currentLanguage === 'en' ? `${days}d ago` : `${days}g önce`;
        }
    }

    formatDate(timestamp) {
        const date = new Date(timestamp);
        const options = { 
            year: 'numeric', 
            month: 'short', 
            day: 'numeric', 
            hour: '2-digit', 
            minute: '2-digit' 
        };
        return date.toLocaleDateString(this.currentLanguage === 'en' ? 'en-US' : 'tr-TR', options);
    }

    loadContactSubmissions() {
        const contacts = this.getContactSubmissions();
        const searchTerm = document.getElementById('contactSearch').value.toLowerCase();
        const statusFilter = document.getElementById('contactFilter').value;

        let filteredContacts = contacts;

        if (searchTerm) {
            filteredContacts = filteredContacts.filter(item =>
                item.data.name.toLowerCase().includes(searchTerm) ||
                item.data.email.toLowerCase().includes(searchTerm)
            );
        }

        if (statusFilter !== 'all') {
            filteredContacts = filteredContacts.filter(item => item.status === statusFilter);
        }

        const contactsList = document.getElementById('contactsList');

        if (filteredContacts.length === 0) {
            contactsList.innerHTML = `
                <div class="submission-item">
                    <p>${this.currentLanguage === 'en' ? 'No contact submissions found' : 'İletişim formu bulunamadı'}</p>
                </div>
            `;
            return;
        }

        contactsList.innerHTML = filteredContacts.map(item => `
            <div class="submission-item ${item.status}" onclick="adminPanel.showSubmissionModal('${item.id}', 'contact')">
                <div class="submission-header">
                    <div class="submission-info">
                        <h4>${item.data.name}</h4>
                        <p>${item.data.email}</p>
                    </div>
                    <div class="submission-meta">
                        <div class="submission-status ${item.status}">${this.getStatusText(item.status)}</div>
                        <div>${this.formatDate(item.timestamp)}</div>
                    </div>
                </div>
                <div class="submission-preview">
                    ${item.data.message}
                </div>
            </div>
        `).join('');
    }

    loadBookingSubmissions() {
        const bookings = this.getBookingSubmissions();
        const searchTerm = document.getElementById('bookingSearch').value.toLowerCase();
        const statusFilter = document.getElementById('bookingFilter').value;

        let filteredBookings = bookings;

        if (searchTerm) {
            filteredBookings = filteredBookings.filter(item =>
                item.data.name.toLowerCase().includes(searchTerm) ||
                item.data.email.toLowerCase().includes(searchTerm)
            );
        }

        if (statusFilter !== 'all') {
            filteredBookings = filteredBookings.filter(item => item.status === statusFilter);
        }

        const bookingsList = document.getElementById('bookingsList');

        if (filteredBookings.length === 0) {
            bookingsList.innerHTML = `
                <div class="submission-item">
                    <p>${this.currentLanguage === 'en' ? 'No booking requests found' : 'Randevu talebi bulunamadı'}</p>
                </div>
            `;
            return;
        }

        bookingsList.innerHTML = filteredBookings.map(item => {
            const preferredDate = new Date(item.data.preferredDate).toLocaleDateString();
            const timeOptions = {
                '09:00': '9:00 AM',
                '10:00': '10:00 AM',
                '11:00': '11:00 AM',
                '14:00': '2:00 PM',
                '15:00': '3:00 PM',
                '16:00': '4:00 PM'
            };
            const preferredTime = timeOptions[item.data.preferredTime] || item.data.preferredTime;

            return `
                <div class="submission-item ${item.status}" onclick="adminPanel.showSubmissionModal('${item.id}', 'booking')">
                    <div class="submission-header">
                        <div class="submission-info">
                            <h4>${item.data.name}</h4>
                            <p>${item.data.email}</p>
                            <p><strong>${this.currentLanguage === 'en' ? 'Preferred:' : 'Tercih:'}</strong> ${preferredDate} ${this.currentLanguage === 'en' ? 'at' : 'saat'} ${preferredTime}</p>
                        </div>
                        <div class="submission-meta">
                            <div class="submission-status ${item.status}">${this.getStatusText(item.status)}</div>
                            <div>${this.formatDate(item.timestamp)}</div>
                        </div>
                    </div>
                    <div class="submission-preview">
                        ${item.data.consultationGoals}
                    </div>
                </div>
            `;
        }).join('');
    }

    getStatusText(status) {
        const statusTexts = {
            en: {
                unread: 'Unread',
                read: 'Read',
                processed: 'Processed'
            },
            tr: {
                unread: 'Okunmamış',
                read: 'Okunmuş',
                processed: 'İşlenmiş'
            }
        };
        return statusTexts[this.currentLanguage][status] || status;
    }

    filterSubmissions(type) {
        if (type === 'contact') {
            this.loadContactSubmissions();
        } else if (type === 'booking') {
            this.loadBookingSubmissions();
        }
    }

    showSubmissionModal(id, type) {
        const submissions = type === 'contact' ? this.getContactSubmissions() : this.getBookingSubmissions();
        const submission = submissions.find(item => item.id === id);

        if (!submission) return;

        this.currentSubmission = { id, type };

        const modal = document.getElementById('submissionModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');

        modalTitle.textContent = type === 'contact' ?
            (this.currentLanguage === 'en' ? 'Contact Form Submission' : 'İletişim Formu Başvurusu') :
            (this.currentLanguage === 'en' ? 'Booking Request Details' : 'Randevu Talebi Detayları');

        if (type === 'contact') {
            modalBody.innerHTML = `
                <div class="submission-details">
                    <div class="detail-row">
                        <strong>${this.currentLanguage === 'en' ? 'Name:' : 'Ad:'}</strong>
                        <span>${submission.data.name}</span>
                    </div>
                    <div class="detail-row">
                        <strong>${this.currentLanguage === 'en' ? 'Email:' : 'E-posta:'}</strong>
                        <span>${submission.data.email}</span>
                    </div>
                    ${submission.data.service ? `
                        <div class="detail-row">
                            <strong>${this.currentLanguage === 'en' ? 'Service Interest:' : 'İlgilenilen Hizmet:'}</strong>
                            <span>${submission.data.service}</span>
                        </div>
                    ` : ''}
                    ${submission.data.budget ? `
                        <div class="detail-row">
                            <strong>${this.currentLanguage === 'en' ? 'Budget Range:' : 'Bütçe Aralığı:'}</strong>
                            <span>${submission.data.budget}</span>
                        </div>
                    ` : ''}
                    <div class="detail-row">
                        <strong>${this.currentLanguage === 'en' ? 'Message:' : 'Mesaj:'}</strong>
                        <div class="message-content">${submission.data.message}</div>
                    </div>
                    <div class="detail-row">
                        <strong>${this.currentLanguage === 'en' ? 'Submitted:' : 'Gönderilme:'}</strong>
                        <span>${this.formatDate(submission.timestamp)}</span>
                    </div>
                    <div class="detail-row">
                        <strong>${this.currentLanguage === 'en' ? 'Status:' : 'Durum:'}</strong>
                        <span class="submission-status ${submission.status}">${this.getStatusText(submission.status)}</span>
                    </div>
                </div>
            `;
        } else {
            const preferredDate = new Date(submission.data.preferredDate).toLocaleDateString();
            const timeOptions = {
                '09:00': '9:00 AM',
                '10:00': '10:00 AM',
                '11:00': '11:00 AM',
                '14:00': '2:00 PM',
                '15:00': '3:00 PM',
                '16:00': '4:00 PM'
            };
            const preferredTime = timeOptions[submission.data.preferredTime] || submission.data.preferredTime;

            modalBody.innerHTML = `
                <div class="submission-details">
                    <div class="detail-row">
                        <strong>${this.currentLanguage === 'en' ? 'Name:' : 'Ad:'}</strong>
                        <span>${submission.data.name}</span>
                    </div>
                    <div class="detail-row">
                        <strong>${this.currentLanguage === 'en' ? 'Email:' : 'E-posta:'}</strong>
                        <span>${submission.data.email}</span>
                    </div>
                    <div class="detail-row">
                        <strong>${this.currentLanguage === 'en' ? 'Preferred Date:' : 'Tercih Edilen Tarih:'}</strong>
                        <span>${preferredDate}</span>
                    </div>
                    <div class="detail-row">
                        <strong>${this.currentLanguage === 'en' ? 'Preferred Time:' : 'Tercih Edilen Saat:'}</strong>
                        <span>${preferredTime}</span>
                    </div>
                    <div class="detail-row">
                        <strong>${this.currentLanguage === 'en' ? 'Consultation Goals:' : 'Danışmanlık Hedefleri:'}</strong>
                        <div class="message-content">${submission.data.consultationGoals}</div>
                    </div>
                    <div class="detail-row">
                        <strong>${this.currentLanguage === 'en' ? 'Submitted:' : 'Gönderilme:'}</strong>
                        <span>${this.formatDate(submission.timestamp)}</span>
                    </div>
                    <div class="detail-row">
                        <strong>${this.currentLanguage === 'en' ? 'Status:' : 'Durum:'}</strong>
                        <span class="submission-status ${submission.status}">${this.getStatusText(submission.status)}</span>
                    </div>
                </div>
            `;
        }

        modal.style.display = 'block';

        // Mark as read if it's unread
        if (submission.status === 'unread') {
            this.updateSubmissionStatus('read');
        }
    }

    closeModal() {
        document.getElementById('submissionModal').style.display = 'none';
        this.currentSubmission = null;
    }

    updateSubmissionStatus(status) {
        if (!this.currentSubmission) return;

        const { id, type } = this.currentSubmission;
        const storageKey = type === 'contact' ? 'contactSubmissions' : 'bookingSubmissions';
        const submissions = JSON.parse(localStorage.getItem(storageKey) || '[]');
        const index = submissions.findIndex(item => item.id === id);

        if (index !== -1) {
            submissions[index].status = status;
            localStorage.setItem(storageKey, JSON.stringify(submissions));

            // Update displays
            this.updateNotificationBadges();
            this.loadOverviewData();

            if (this.currentSection === 'contacts') {
                this.loadContactSubmissions();
            } else if (this.currentSection === 'bookings') {
                this.loadBookingSubmissions();
            }

            // Update modal
            const statusElement = document.querySelector('.modal-body .submission-status');
            if (statusElement) {
                statusElement.className = `submission-status ${status}`;
                statusElement.textContent = this.getStatusText(status);
            }

            this.showNotification(
                this.currentLanguage === 'en' ?
                `Status updated to ${this.getStatusText(status)}` :
                `Durum ${this.getStatusText(status)} olarak güncellendi`,
                'success'
            );
        }
    }

    deleteCurrentSubmission() {
        if (!this.currentSubmission) return;

        const confirmMessage = this.currentLanguage === 'en' ?
            'Are you sure you want to delete this submission?' :
            'Bu başvuruyu silmek istediğinizden emin misiniz?';

        if (!confirm(confirmMessage)) return;

        const { id, type } = this.currentSubmission;
        const storageKey = type === 'contact' ? 'contactSubmissions' : 'bookingSubmissions';
        const submissions = JSON.parse(localStorage.getItem(storageKey) || '[]');
        const filteredSubmissions = submissions.filter(item => item.id !== id);

        localStorage.setItem(storageKey, JSON.stringify(filteredSubmissions));

        // Update displays
        this.updateNotificationBadges();
        this.loadOverviewData();

        if (this.currentSection === 'contacts') {
            this.loadContactSubmissions();
        } else if (this.currentSection === 'bookings') {
            this.loadBookingSubmissions();
        }

        this.closeModal();

        this.showNotification(
            this.currentLanguage === 'en' ? 'Submission deleted successfully' : 'Başvuru başarıyla silindi',
            'success'
        );
    }

    exportData(type) {
        let data, filename;

        if (type === 'contacts') {
            data = this.getContactSubmissions();
            filename = 'contact-submissions.json';
        } else if (type === 'bookings') {
            data = this.getBookingSubmissions();
            filename = 'booking-submissions.json';
        } else {
            data = {
                contactSubmissions: this.getContactSubmissions(),
                bookingSubmissions: this.getBookingSubmissions(),
                exportDate: new Date().toISOString()
            };
            filename = 'all-submissions.json';
        }

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showNotification(
            this.currentLanguage === 'en' ? 'Data exported successfully' : 'Veriler başarıyla dışa aktarıldı',
            'success'
        );
    }

    changePassword() {
        const newPassword = prompt(
            this.currentLanguage === 'en' ? 'Enter new password:' : 'Yeni şifre girin:'
        );

        if (newPassword && newPassword.length >= 6) {
            this.adminPassword = newPassword;
            localStorage.setItem('adminPassword', newPassword);
            this.showNotification(
                this.currentLanguage === 'en' ? 'Password changed successfully' : 'Şifre başarıyla değiştirildi',
                'success'
            );
        } else if (newPassword) {
            this.showNotification(
                this.currentLanguage === 'en' ? 'Password must be at least 6 characters' : 'Şifre en az 6 karakter olmalıdır',
                'error'
            );
        }
    }

    clearAllData() {
        const confirmMessage = this.currentLanguage === 'en' ?
            'Are you sure you want to clear all data? This action cannot be undone.' :
            'Tüm verileri temizlemek istediğinizden emin misiniz? Bu işlem geri alınamaz.';

        if (!confirm(confirmMessage)) return;

        localStorage.removeItem('contactSubmissions');
        localStorage.removeItem('bookingSubmissions');

        // Update displays
        this.updateNotificationBadges();
        this.loadOverviewData();
        this.loadContactSubmissions();
        this.loadBookingSubmissions();

        this.showNotification(
            this.currentLanguage === 'en' ? 'All data cleared successfully' : 'Tüm veriler başarıyla temizlendi',
            'success'
        );
    }

    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.admin-notification');
        existingNotifications.forEach(notification => notification.remove());

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `admin-notification admin-notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
            background: ${type === 'success' ? '#48bb78' : type === 'error' ? '#f56565' : '#4299e1'};
            color: white;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            animation: slideInRight 0.3s ease-out;
        `;

        // Add animation styles if not already added
        if (!document.getElementById('admin-notification-styles')) {
            const style = document.createElement('style');
            style.id = 'admin-notification-styles';
            style.textContent = `
                @keyframes slideInRight {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                .notification-content {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }
                .notification-close {
                    background: none;
                    border: none;
                    color: white;
                    cursor: pointer;
                    margin-left: auto;
                    padding: 0.25rem;
                }
                .submission-details {
                    display: flex;
                    flex-direction: column;
                    gap: 1rem;
                }
                .detail-row {
                    display: flex;
                    flex-direction: column;
                    gap: 0.25rem;
                }
                .detail-row strong {
                    color: #2d3748;
                    font-weight: 600;
                }
                .message-content {
                    background: #f7fafc;
                    padding: 1rem;
                    border-radius: 8px;
                    border-left: 4px solid #667eea;
                    white-space: pre-wrap;
                    line-height: 1.5;
                }
            `;
            document.head.appendChild(style);
        }

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
}



// Initialize admin panel when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing admin panel...');
    window.adminPanel = new AdminPanel();
});
