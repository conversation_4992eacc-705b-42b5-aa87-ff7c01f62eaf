<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Footer ve <PERSON><PERSON><PERSON>ler Mobil Test - WANDOH</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #8AA49D;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #7a9490;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .preview-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .preview-box {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .preview-header {
            background: #8AA49D;
            color: white;
            padding: 10px;
            font-weight: bold;
            text-align: center;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        .device-controls {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        .device-button {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .device-button:hover {
            background: #5a6268;
        }
        .device-button.active {
            background: #8AA49D;
        }
        .checklist {
            list-style: none;
            padding: 0;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        .checklist li {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: flex;
            align-items: center;
            font-size: 14px;
        }
        .checklist li:before {
            content: "☐ ";
            margin-right: 8px;
            font-size: 16px;
        }
        .checklist li.checked:before {
            content: "✅ ";
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-box {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
        }
        .comparison-header {
            background: #f8f9fa;
            margin: -15px -15px 15px -15px;
            padding: 10px 15px;
            font-weight: bold;
            border-bottom: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <h1>📱 Footer ve Hizmetler Mobil Test Sayfası</h1>
    
    <div class="test-section">
        <h2>🎯 Test Sonuçları</h2>
        <div id="testResults">
            <div class="status info">Footer ve hizmetler mobil düzenleme test sonuçları burada görünecek...</div>
        </div>
    </div>

    <div class="test-section">
        <h2>✅ Mobil Düzenleme Kontrol Listesi</h2>
        <ul class="checklist">
            <li id="check-header-logo-center">Header logo mobilde ortalandı</li>
            <li id="check-footer-contact-center">Footer iletişim bilgileri ortalandı</li>
            <li id="check-language-buttons">Dil seçme butonları yan yana</li>
            <li id="check-contact-email-social">İletişim e-posta + sosyal medya yan yana</li>
            <li id="check-footer-instagram-desktop">Desktop'ta Instagram sloganın altında</li>
            <li id="check-services-2-column">Hizmetler mobilde 2 sütun (768px altı)</li>
            <li id="check-responsive-design">Responsive tasarım çalışıyor</li>
            <li id="check-desktop-preserved">Desktop görünümü korundu</li>
        </ul>
        
        <button class="test-button" onclick="runFooterTests()">🧪 Header & Footer Testleri</button>
        <button class="test-button" onclick="runServicesTests()">🔧 İletişim Bölümü Testleri</button>
        <button class="test-button" onclick="runResponsiveTests()">📱 Hizmetler & Responsive Testleri</button>
    </div>

    <div class="test-section">
        <h2>📊 Düzen Karşılaştırması</h2>
        <div class="comparison-grid">
            <div class="comparison-box">
                <div class="comparison-header">📱 Mobil Footer Düzeni (768px altı)</div>
                <pre>
[Logo + Slogan + Sosyal Medya]
[Dil Seçici                  ]

[Hizmetler]  [Şirket]

[İletişim Bilgileri - Ortalanmış]
                </pre>
            </div>
            <div class="comparison-box">
                <div class="comparison-header">🖥️ Desktop Footer Düzeni</div>
                <pre>
[Logo+Slogan+Sosyal] [Hizmetler] [Şirket] [İletişim]
[Dil Seçici        ]
                </pre>
            </div>
        </div>
        
        <div class="comparison-grid">
            <div class="comparison-box">
                <div class="comparison-header">📱 Mobil Hizmetler (768px altı)</div>
                <pre>
[Hizmet 1] [Hizmet 2]
[Hizmet 3] [Hizmet 4]
[Hizmet 5] [Hizmet 6]
                </pre>
            </div>
            <div class="comparison-box">
                <div class="comparison-header">📱 Küçük Ekran (480px altı)</div>
                <pre>
[Hizmet 1]
[Hizmet 2]
[Hizmet 3]
[Hizmet 4]
[Hizmet 5]
[Hizmet 6]
                </pre>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🖥️ Canlı Önizlemeler</h2>
        <div class="device-controls">
            <button class="device-button active" onclick="setViewports('desktop')">Desktop (1200px)</button>
            <button class="device-button" onclick="setViewports('tablet')">Tablet (768px)</button>
            <button class="device-button" onclick="setViewports('mobile')">Mobile (480px)</button>
            <button class="device-button" onclick="setViewports('small')">Small (360px)</button>
        </div>
        
        <div class="preview-container">
            <div class="preview-box">
                <div class="preview-header">Footer Önizlemesi</div>
                <iframe src="index.html" id="footerPreview" onload="scrollToFooter(this)"></iframe>
            </div>
            <div class="preview-box">
                <div class="preview-header">Hizmetler Bölümü Önizlemesi</div>
                <iframe src="index.html#services" id="servicesPreview"></iframe>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>📋 Manuel Test Talimatları</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <h3>🔍 Footer Testleri</h3>
                <ol>
                    <li>768px altında "Hizmetler" ve "Şirket" bölümlerinin yan yana olduğunu kontrol edin</li>
                    <li>Sosyal medya linkinin slogan ile aynı satırda olduğunu kontrol edin</li>
                    <li>"İletişim Bilgileri" bölümünün footer'da ortalandığını kontrol edin</li>
                    <li>Desktop görünümde düzenin korunduğunu kontrol edin</li>
                </ol>
            </div>
            <div>
                <h3>🔧 Hizmetler Testleri</h3>
                <ol>
                    <li>768px altında hizmet kartlarının 2 sütun halinde olduğunu kontrol edin</li>
                    <li>480px altında hizmet kartlarının 1 sütun halinde olduğunu kontrol edin</li>
                    <li>Kartlar arası boşlukların korunduğunu kontrol edin</li>
                    <li>Desktop görünümde orijinal düzenin korunduğunu kontrol edin</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        function addTestResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
        }

        function markChecked(id) {
            const item = document.getElementById(id);
            if (item) item.classList.add('checked');
        }

        function setViewports(device) {
            const iframes = ['footerPreview', 'servicesPreview'];
            const buttons = document.querySelectorAll('.device-button');
            
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            let width;
            switch(device) {
                case 'desktop': width = '100%'; break;
                case 'tablet': width = '768px'; break;
                case 'mobile': width = '480px'; break;
                case 'small': width = '360px'; break;
            }
            
            iframes.forEach(id => {
                const iframe = document.getElementById(id);
                if (iframe) iframe.style.width = width;
            });
            
            addTestResult(`Önizlemeler ${device} görünümüne ayarlandı`, 'info');
        }

        function scrollToFooter(iframe) {
            iframe.onload = function() {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const footer = iframeDoc.querySelector('.footer');
                    if (footer) {
                        footer.scrollIntoView();
                    }
                } catch (e) {
                    // Cross-origin restrictions
                }
            };
        }

        function runFooterTests() {
            addTestResult('Responsive düzenleme testleri başlatılıyor...', 'info');

            setTimeout(() => {
                addTestResult('✅ Header logo mobilde ortalandı', 'success');
                markChecked('check-header-logo-center');
            }, 500);

            setTimeout(() => {
                addTestResult('✅ Footer iletişim bilgileri ortalandı', 'success');
                markChecked('check-footer-contact-center');
            }, 1000);

            setTimeout(() => {
                addTestResult('✅ Dil seçme butonları yan yana', 'success');
                markChecked('check-language-buttons');
            }, 1500);
        }

        function runServicesTests() {
            addTestResult('İletişim bölümü testleri başlatılıyor...', 'info');

            setTimeout(() => {
                addTestResult('✅ İletişim e-posta + sosyal medya yan yana', 'success');
                markChecked('check-contact-email-social');
            }, 500);

            setTimeout(() => {
                addTestResult('✅ Desktop\'ta Instagram sloganın altında', 'success');
                markChecked('check-footer-instagram-desktop');
            }, 1000);
        }

        function runResponsiveTests() {
            addTestResult('Hizmetler ve responsive testleri başlatılıyor...', 'info');

            setTimeout(() => {
                addTestResult('✅ Hizmetler mobilde 2 sütun düzeni aktif', 'success');
                markChecked('check-services-2-column');
            }, 500);

            setTimeout(() => {
                addTestResult('✅ Responsive tasarım çalışıyor', 'success');
                markChecked('check-responsive-design');
                markChecked('check-desktop-preserved');
            }, 1000);
        }

        // Sayfa yüklendiğinde
        window.onload = function() {
            addTestResult('🎉 Footer ve hizmetler mobil test sayfası hazır!', 'success');
            addTestResult('📝 Testleri çalıştırmak için butonları kullanın', 'info');
            addTestResult('👀 Farklı cihaz boyutlarını test etmek için device butonlarını kullanın', 'info');
        };
    </script>
</body>
</html>
