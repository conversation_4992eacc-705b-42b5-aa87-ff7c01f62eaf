<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON><PERSON> Formu Test - WANDOH</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #8AA49D;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #7a9490;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .preview-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .preview-box {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .preview-header {
            background: #8AA49D;
            color: white;
            padding: 10px;
            font-weight: bold;
            text-align: center;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        .responsive-test {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }
        .device-button {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .device-button:hover {
            background: #5a6268;
        }
        .device-button.active {
            background: #8AA49D;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        .checklist li:before {
            content: "☐ ";
            margin-right: 10px;
            font-size: 16px;
        }
        .checklist li.checked:before {
            content: "✅ ";
        }
    </style>
</head>
<body>
    <h1>📝 İletişim Formu Düzen Testi</h1>
    
    <div class="test-section">
        <h2>🎯 Test Sonuçları</h2>
        <div id="testResults">
            <div class="status info">Form düzeni test sonuçları burada görünecek...</div>
        </div>
        
        <div class="responsive-test">
            <h3>📱 Responsive Test:</h3>
            <button class="device-button active" onclick="setViewport('desktop')">Desktop (1200px)</button>
            <button class="device-button" onclick="setViewport('tablet')">Tablet (768px)</button>
            <button class="device-button" onclick="setViewport('mobile')">Mobile (480px)</button>
        </div>
    </div>

    <div class="test-section">
        <h2>✅ Form Düzen Kontrol Listesi</h2>
        <ul class="checklist">
            <li id="check-name-service">Ad Soyad ve İlgilenilen Hizmet yan yana</li>
            <li id="check-email-budget">E-posta ve Bütçe Aralığı yan yana</li>
            <li id="check-responsive-tablet">Tablet görünümde düzen korunuyor</li>
            <li id="check-responsive-mobile">Mobil görünümde alt alta geçiyor</li>
            <li id="check-form-validation">Form validasyonları çalışıyor</li>
            <li id="check-language-support">Dil değiştirme çalışıyor</li>
            <li id="check-submit-function">Form gönderme çalışıyor</li>
        </ul>
        
        <button class="test-button" onclick="runFormTests()">🧪 Otomatik Testleri Çalıştır</button>
        <button class="test-button" onclick="testFormValidation()">✔️ Validasyon Test Et</button>
        <button class="test-button" onclick="testLanguageSwitch()">🌐 Dil Değiştirme Test Et</button>
    </div>

    <div class="preview-container">
        <div class="preview-box">
            <div class="preview-header">İletişim Formu - Canlı Önizleme</div>
            <iframe src="index.html#contact" id="formPreview"></iframe>
        </div>
        <div class="preview-box">
            <div class="preview-header">Responsive Test Alanı</div>
            <iframe src="index.html#contact" id="responsivePreview" style="width: 100%; transition: width 0.3s ease;"></iframe>
        </div>
    </div>

    <div class="test-section">
        <h2>📋 Manuel Test Talimatları</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <h3>🖥️ Desktop Test</h3>
                <ol>
                    <li>Ad Soyad ve İlgilenilen Hizmet alanlarının yan yana olduğunu kontrol edin</li>
                    <li>E-posta ve Bütçe Aralığı alanlarının yan yana olduğunu kontrol edin</li>
                    <li>Alanlar arasında %4 boşluk olduğunu kontrol edin</li>
                    <li>Her alanın yaklaşık %48 genişlikte olduğunu kontrol edin</li>
                </ol>
            </div>
            <div>
                <h3>📱 Mobil Test</h3>
                <ol>
                    <li>Tarayıcı genişliğini 768px altına düşürün</li>
                    <li>Tüm form alanlarının alt alta geçtiğini kontrol edin</li>
                    <li>Form alanlarının tam genişlikte olduğunu kontrol edin</li>
                    <li>Alanlar arası boşlukların korunduğunu kontrol edin</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        function addTestResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
        }

        function markChecked(id) {
            const item = document.getElementById(id);
            if (item) item.classList.add('checked');
        }

        function setViewport(device) {
            const iframe = document.getElementById('responsivePreview');
            const buttons = document.querySelectorAll('.device-button');
            
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            switch(device) {
                case 'desktop':
                    iframe.style.width = '100%';
                    addTestResult('Desktop görünüm (1200px) aktif', 'info');
                    break;
                case 'tablet':
                    iframe.style.width = '768px';
                    addTestResult('Tablet görünüm (768px) aktif', 'info');
                    break;
                case 'mobile':
                    iframe.style.width = '480px';
                    addTestResult('Mobil görünüm (480px) aktif', 'info');
                    break;
            }
        }

        function runFormTests() {
            addTestResult('Form düzen testleri başlatılıyor...', 'info');
            
            const iframe = document.getElementById('formPreview');
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            
            try {
                // Form row'ları kontrol et
                const formRows = iframeDoc.querySelectorAll('.form-row');
                if (formRows.length >= 2) {
                    addTestResult('✅ Form row yapısı bulundu', 'success');
                    markChecked('check-name-service');
                    markChecked('check-email-budget');
                } else {
                    addTestResult('❌ Form row yapısı bulunamadı', 'error');
                }

                // Form elementlerini kontrol et
                const nameField = iframeDoc.getElementById('name');
                const serviceField = iframeDoc.getElementById('service');
                const emailField = iframeDoc.getElementById('email');
                const budgetField = iframeDoc.getElementById('budget');

                if (nameField && serviceField && emailField && budgetField) {
                    addTestResult('✅ Tüm form alanları bulundu', 'success');
                    markChecked('check-form-validation');
                } else {
                    addTestResult('❌ Bazı form alanları bulunamadı', 'error');
                }

            } catch (error) {
                addTestResult(`❌ Test hatası: ${error.message}`, 'error');
            }
        }

        function testFormValidation() {
            addTestResult('Form validasyon testi başlatılıyor...', 'info');
            
            const iframe = document.getElementById('formPreview');
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            
            try {
                const form = iframeDoc.getElementById('contactForm');
                const submitButton = form.querySelector('button[type="submit"]');
                
                // Boş form göndermeyi dene
                submitButton.click();
                
                setTimeout(() => {
                    const nameField = iframeDoc.getElementById('name');
                    if (nameField.validity && !nameField.validity.valid) {
                        addTestResult('✅ Form validasyonu çalışıyor', 'success');
                        markChecked('check-form-validation');
                    } else {
                        addTestResult('❌ Form validasyonu çalışmıyor', 'error');
                    }
                }, 500);
                
            } catch (error) {
                addTestResult(`❌ Validasyon test hatası: ${error.message}`, 'error');
            }
        }

        function testLanguageSwitch() {
            addTestResult('Dil değiştirme testi başlatılıyor...', 'info');
            
            const iframe = document.getElementById('formPreview');
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            
            try {
                const langButtons = iframeDoc.querySelectorAll('.lang-btn');
                if (langButtons.length > 0) {
                    addTestResult('✅ Dil değiştirme butonları bulundu', 'success');
                    markChecked('check-language-support');
                } else {
                    addTestResult('❌ Dil değiştirme butonları bulunamadı', 'error');
                }
            } catch (error) {
                addTestResult(`❌ Dil test hatası: ${error.message}`, 'error');
            }
        }

        // Sayfa yüklendiğinde
        window.onload = function() {
            addTestResult('🎉 Form düzen test sayfası hazır!', 'success');
            addTestResult('📝 Otomatik testleri çalıştırmak için butonları kullanın', 'info');
            addTestResult('👀 Sağ taraftaki önizlemeleri kullanarak manuel test yapın', 'info');
        };
    </script>
</body>
</html>
