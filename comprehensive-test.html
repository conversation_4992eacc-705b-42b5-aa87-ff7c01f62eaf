<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WANDOH - Kapsamlı Test Sayfası</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #8AA49D;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #7a9490;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .checklist li:before {
            content: "☐ ";
            margin-right: 10px;
        }
        .checklist li.checked:before {
            content: "✅ ";
        }
    </style>
</head>
<body>
    <h1>🔧 WANDOH - Kapsamlı Test Sayfası</h1>
    
    <div class="test-section">
        <h2>📋 Test Sonuçları</h2>
        <div id="testResults">
            <div class="status info">Test sonuçları burada görünecek...</div>
        </div>
    </div>

    <div class="test-grid">
        <div class="test-section">
            <h2>🔐 Admin Panel Testleri</h2>
            <button class="test-button" onclick="testAdminLogin()">Login Test</button>
            <button class="test-button" onclick="testPasswordToggle()">Şifre Toggle Test</button>
            <button class="test-button" onclick="openAdminPanel()">Admin Panel Aç</button>
            
            <h3>✅ Admin Panel Kontrol Listesi</h3>
            <ul class="checklist">
                <li id="admin-login">Login fonksiyonu çalışıyor</li>
                <li id="admin-password-toggle">Şifre toggle çalışıyor</li>
                <li id="admin-enter-key">Enter tuşu ile giriş</li>
                <li id="admin-error-message">Hata mesajları görünüyor</li>
                <li id="admin-dashboard">Dashboard açılıyor</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🌐 Ana Sayfa Testleri</h2>
            <button class="test-button" onclick="testLanguageSwitching()">Dil Değiştirme Test</button>
            <button class="test-button" onclick="testForms()">Form Testleri</button>
            <button class="test-button" onclick="openIndexPage()">Ana Sayfa Aç</button>
            <button class="test-button" onclick="openFormTest()">Form Düzen Test</button>
            
            <h3>✅ Ana Sayfa Kontrol Listesi</h3>
            <ul class="checklist">
                <li id="index-language">Dil değiştirme çalışıyor</li>
                <li id="index-contact-form">İletişim formu çalışıyor</li>
                <li id="index-booking-form">Randevu formu çalışıyor</li>
                <li id="index-navigation">Navigasyon çalışıyor</li>
                <li id="index-responsive">Responsive tasarım</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🎯 Manuel Test Talimatları</h2>
        <div class="test-grid">
            <div>
                <h3>Admin Panel</h3>
                <ol>
                    <li><strong>Şifre:</strong> <code>M4qsxWandoh7t.</code></li>
                    <li>Şifreyi girin ve login butonuna tıklayın</li>
                    <li>Göz ikonuna tıklayarak şifre görünürlüğünü test edin</li>
                    <li>Enter tuşu ile giriş yapmayı deneyin</li>
                    <li>Yanlış şifre ile hata mesajını test edin</li>
                </ol>
            </div>
            <div>
                <h3>Ana Sayfa</h3>
                <ol>
                    <li>Dil değiştirme butonlarını test edin</li>
                    <li>İletişim formunu doldurup gönderin</li>
                    <li>Randevu formunu doldurup gönderin</li>
                    <li>Navigasyon linklerini test edin</li>
                    <li>Mobil görünümü test edin</li>
                </ol>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🖥️ Canlı Önizlemeler</h2>
        <div class="test-grid">
            <div>
                <h3>Admin Panel</h3>
                <iframe src="admin.html" id="adminFrame"></iframe>
            </div>
            <div>
                <h3>Ana Sayfa</h3>
                <iframe src="index.html" id="indexFrame"></iframe>
            </div>
        </div>
    </div>

    <script>
        function addTestResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
        }

        function markChecked(id) {
            const item = document.getElementById(id);
            if (item) item.classList.add('checked');
        }

        function testAdminLogin() {
            addTestResult('Admin login testi başlatılıyor...', 'info');
            
            const iframe = document.getElementById('adminFrame');
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            
            try {
                const passwordInput = iframeDoc.getElementById('password');
                const loginButton = iframeDoc.querySelector('button[type="submit"]');
                
                if (passwordInput && loginButton) {
                    passwordInput.value = 'M4qsxWandoh7t.';
                    loginButton.click();
                    
                    setTimeout(() => {
                        const dashboard = iframeDoc.getElementById('adminDashboard');
                        if (dashboard && dashboard.style.display !== 'none') {
                            addTestResult('✅ Admin login testi BAŞARILI', 'success');
                            markChecked('admin-login');
                            markChecked('admin-dashboard');
                        } else {
                            addTestResult('❌ Admin login testi BAŞARISIZ', 'error');
                        }
                    }, 1000);
                } else {
                    addTestResult('❌ Admin login elementleri bulunamadı', 'error');
                }
            } catch (error) {
                addTestResult(`❌ Admin login test hatası: ${error.message}`, 'error');
            }
        }

        function testPasswordToggle() {
            addTestResult('Şifre toggle testi başlatılıyor...', 'info');
            
            const iframe = document.getElementById('adminFrame');
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            
            try {
                const passwordInput = iframeDoc.getElementById('password');
                const toggleButton = iframeDoc.getElementById('passwordToggle');
                
                if (passwordInput && toggleButton) {
                    const initialType = passwordInput.type;
                    toggleButton.click();
                    
                    setTimeout(() => {
                        if (passwordInput.type !== initialType) {
                            addTestResult('✅ Şifre toggle testi BAŞARILI', 'success');
                            markChecked('admin-password-toggle');
                        } else {
                            addTestResult('❌ Şifre toggle testi BAŞARISIZ', 'error');
                        }
                    }, 100);
                } else {
                    addTestResult('❌ Toggle elementleri bulunamadı', 'error');
                }
            } catch (error) {
                addTestResult(`❌ Toggle test hatası: ${error.message}`, 'error');
            }
        }

        function testLanguageSwitching() {
            addTestResult('Dil değiştirme testi başlatılıyor...', 'info');
            
            const iframe = document.getElementById('indexFrame');
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            
            try {
                const langButtons = iframeDoc.querySelectorAll('.lang-btn');
                if (langButtons.length > 0) {
                    addTestResult('✅ Dil butonları bulundu', 'success');
                    markChecked('index-language');
                } else {
                    addTestResult('❌ Dil butonları bulunamadı', 'error');
                }
            } catch (error) {
                addTestResult(`❌ Dil test hatası: ${error.message}`, 'error');
            }
        }

        function testForms() {
            addTestResult('Form testleri başlatılıyor...', 'info');
            
            const iframe = document.getElementById('indexFrame');
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            
            try {
                const contactForm = iframeDoc.getElementById('contactForm');
                const bookingForm = iframeDoc.getElementById('bookingForm');
                
                if (contactForm) {
                    addTestResult('✅ İletişim formu bulundu', 'success');
                    markChecked('index-contact-form');
                }
                
                if (bookingForm) {
                    addTestResult('✅ Randevu formu bulundu', 'success');
                    markChecked('index-booking-form');
                }
            } catch (error) {
                addTestResult(`❌ Form test hatası: ${error.message}`, 'error');
            }
        }

        function openAdminPanel() {
            window.open('admin.html', '_blank');
            addTestResult('🚀 Admin paneli yeni sekmede açıldı', 'info');
        }

        function openIndexPage() {
            window.open('index.html', '_blank');
            addTestResult('🚀 Ana sayfa yeni sekmede açıldı', 'info');
        }

        function openFormTest() {
            window.open('form-test.html', '_blank');
            addTestResult('🚀 Form düzen test sayfası açıldı', 'info');
        }

        // Sayfa yüklendiğinde
        window.onload = function() {
            addTestResult('🎉 Kapsamlı test sayfası hazır!', 'success');
            addTestResult('📝 Manuel testleri başlatmak için butonları kullanın', 'info');
        };
    </script>
</body>
</html>
