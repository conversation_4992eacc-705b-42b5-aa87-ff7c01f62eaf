/* Import Futura Font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Admin Panel Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Futura', 'Inter', sans-serif;
    background: #f8fafc;
    color: #000000;
    line-height: 1.6;
}

/* Login Screen */
.login-screen {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #8AA49D;
}

.login-container {
    background: white;
    padding: 3rem;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-header img {
    height: 60px;
    width: auto;
    max-width: 200px;
    object-fit: contain;
    margin-bottom: 1rem;
}

.login-header h1 {
    color: #8AA49D;
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.login-header h2 {
    color: #2d3748;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.login-header p {
    color: #666;
    margin-bottom: 2rem;
}

.login-form .form-group {
    margin-bottom: 1.5rem;
    text-align: left;
}

.login-form label {
    display: block;
    margin-bottom: 0.5rem;
    color: #2d3748;
    font-weight: 500;
}

.login-form input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.login-form input:focus {
    outline: none;
    border-color: #8AA49D;
}

.password-input-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 4px;
    font-size: 1rem;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #8AA49D;
}

.password-toggle:focus {
    outline: none;
    color: #8AA49D;
}

.login-error {
    background: #fed7d7;
    color: #c53030;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    font-family: inherit;
}

.btn-primary {
    background: #8AA49D;
    color: white;
}

.btn-primary:hover {
    background: #7a9490;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
    border: 1px solid #cbd5e0;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

.btn-success {
    background: #48bb78;
    color: white;
}

.btn-success:hover {
    background: #38a169;
}

.btn-danger {
    background: #f56565;
    color: white;
}

.btn-danger:hover {
    background: #e53e3e;
}

/* Admin Dashboard */
.admin-dashboard {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.admin-header {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 1rem 2rem;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.header-left img {
    height: 35px;
    width: auto;
    max-width: 120px;
    object-fit: contain;
}

.header-left h1 {
    color: #2d3748;
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-size: 0.9rem;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Language Selector */
.language-selector {
    display: flex;
    gap: 0.25rem;
}

.lang-btn {
    background: transparent;
    border: 2px solid #e2e8f0;
    color: #666;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.lang-btn:hover {
    border-color: #8AA49D;
    color: #8AA49D;
}

.lang-btn.active {
    background: #8AA49D;
    color: white;
    border-color: #8AA49D;
}

/* Navigation */
.admin-nav {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 0 2rem;
}

.nav-content {
    display: flex;
    gap: 1rem;
    max-width: 1400px;
    margin: 0 auto;
}

.nav-btn {
    background: transparent;
    border: none;
    padding: 1rem 1.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-weight: 500;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    position: relative;
}

.nav-btn:hover {
    color: #8AA49D;
    background: #f7fafc;
}

.nav-btn.active {
    color: #8AA49D;
    border-bottom-color: #8AA49D;
}

.notification-badge {
    background: #f56565;
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    position: absolute;
    top: 8px;
    right: 8px;
}

.notification-badge:empty {
    display: none;
}

/* Main Content */
.admin-main {
    flex: 1;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

.admin-section {
    display: none;
}

.admin-section.active {
    display: block;
}

.section-header {
    margin-bottom: 2rem;
}

.section-header h2 {
    color: #2d3748;
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.section-header p {
    color: #666;
    font-size: 1.1rem;
}

.section-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-top: 1rem;
}

/* Search Box */
.search-box {
    position: relative;
    flex: 1;
    max-width: 300px;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.search-box input {
    width: 100%;
    padding: 10px 12px 10px 40px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
}

.search-box input:focus {
    outline: none;
    border-color: #8AA49D;
}

.filter-select {
    padding: 10px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #2d3748;
    cursor: pointer;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: #8AA49D;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-content h3 {
    font-size: 2rem;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

/* Recent Activity */
.recent-activity {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.recent-activity h3 {
    color: #2d3748;
    margin-bottom: 1.5rem;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: #edf2f7;
    transform: translateY(-1px);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.activity-icon.contact {
    background: #8AA49D;
}

.activity-icon.booking {
    background: #48bb78;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.activity-content p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

.activity-time {
    color: #666;
    font-size: 0.8rem;
}

/* Submissions List */
.submissions-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.submission-item {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.submission-item:last-child {
    border-bottom: none;
}

.submission-item:hover {
    background: #f7fafc;
}

.submission-item.unread {
    border-left: 4px solid #8AA49D;
    background: #f0f8f6;
}

.submission-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.submission-info h4 {
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.submission-info p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

.submission-meta {
    text-align: right;
    font-size: 0.8rem;
    color: #666;
}

.submission-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
    margin-bottom: 0.5rem;
}

.submission-status.unread {
    background: #fed7d7;
    color: #c53030;
}

.submission-status.read {
    background: #bee3f8;
    color: #2b6cb0;
}

.submission-status.processed {
    background: #c6f6d5;
    color: #276749;
}

.submission-preview {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
    max-height: 3.6em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

/* Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.setting-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.setting-card h3 {
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.setting-card p {
    color: #666;
    margin-bottom: 1.5rem;
}

.setting-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #2d3748;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-header img {
        height: 50px;
        max-width: 150px;
    }

    .header-left img {
        height: 30px;
        max-width: 100px;
    }

    .admin-header,
    .admin-nav {
        padding: 1rem;
    }
    
    .admin-main {
        padding: 1rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .nav-content {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .nav-btn {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
    
    .section-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        max-width: none;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .setting-actions {
        flex-direction: column;
    }
    
    .modal-content {
        margin: 10% auto;
        width: 95%;
    }
    
    .modal-footer {
        flex-direction: column;
    }
}
