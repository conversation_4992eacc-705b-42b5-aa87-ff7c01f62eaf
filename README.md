# WANDOH Agency - Professional Landing Page

A modern, responsive landing page for a digital marketing and social media agency built with HTML5, CSS3, and JavaScript.

## 🚀 Features

### Core Sections
- **Hero Section** - Eye-catching header with agency branding and value proposition
- **About/CV Section** - Professional background and team expertise showcase
- **Services Section** - Detailed service offerings with pricing
- **Testimonials** - Client reviews and success stories
- **Contact Section** - Professional contact form with email integration
- **Appointment Booking** - Consultation scheduling form
- **Footer** - Complete site navigation and contact information

### Technical Features
- **Responsive Design** - Optimized for desktop, tablet, and mobile devices
- **Modern UI/UX** - Professional design with smooth animations
- **Email Integration** - Contact and booking forms redirect to email client
- **Form Validation** - Client-side validation for all forms
- **Smooth Scrolling** - Enhanced navigation experience
- **Mobile Navigation** - Hamburger menu for mobile devices
- **Loading States** - Visual feedback for form submissions
- **Notification System** - User feedback for form interactions

## 📁 Project Structure

```
digital-marketing-agency/
├── index.html          # Main website HTML file
├── styles.css          # Main website CSS styling
├── script.js           # Main website JavaScript functionality
├── admin.html          # Admin panel HTML file
├── admin-styles.css    # Admin panel CSS styling
├── admin-script.js     # Admin panel JavaScript functionality
├── deploy.html         # Deployment guide
└── README.md           # Project documentation
```

## 🎨 Design Features

### Color Scheme
- **Primary Gradient**: Purple to Blue (#667eea to #764ba2)
- **Background**: Light gray gradients (#f5f7fa to #c3cfe2)
- **Text**: Dark gray (#2d3748) for headings, medium gray (#666) for body
- **Accent**: Green (#10b981) for success states

### Typography
- **Font Family**: Inter (Google Fonts)
- **Weights**: 300, 400, 500, 600, 700
- **Responsive sizing** with proper hierarchy

### Components
- **Floating Cards** - Animated service icons in hero section
- **Service Cards** - Feature-rich service descriptions with pricing
- **Profile Cards** - Team member showcase
- **Testimonial Cards** - Client review display
- **Form Components** - Professional form styling

## 📱 Responsive Breakpoints

- **Desktop**: 1200px and above
- **Tablet**: 768px to 1199px
- **Mobile**: 767px and below
- **Small Mobile**: 480px and below

## 🛠️ Technologies Used

- **HTML5** - Semantic markup and modern structure
- **CSS3** - Advanced styling with Flexbox and Grid
- **JavaScript (ES6+)** - Modern JavaScript features
- **Font Awesome** - Professional icons
- **Google Fonts** - Typography (Inter font family)

## 🔐 Admin Panel

A comprehensive admin panel is included for managing form submissions and booking requests:

### Admin Panel Features
- **Secure Login**: Password-protected access (default: `M4qsxWandoh7t.`)
- **Password Visibility Toggle**: Eye icon to show/hide password during login
- **Dashboard Overview**: Statistics and recent activity summary
- **Contact Management**: View, filter, and manage contact form submissions
- **Booking Management**: Handle consultation booking requests
- **Status Tracking**: Mark submissions as read/unread or processed/pending
- **Data Export**: Export submissions to JSON format
- **Bilingual Support**: Full Turkish and English language support
- **Responsive Design**: Works perfectly on desktop and mobile devices

### Admin Panel Access
1. Navigate to `admin.html` in your browser
2. Enter the admin password: `M4qsxWandoh7t.`
3. Use the eye icon to show/hide the password while typing
4. Manage all incoming submissions from the dashboard

### Admin Panel Sections
- **Overview**: Dashboard with statistics and recent activity
- **Contact Forms**: Manage all contact form submissions
- **Booking Requests**: Handle consultation appointment requests
- **Settings**: Change password and manage data

## 📧 Email Integration

The contact and booking forms are configured to open the user's default email client with pre-filled content AND save data to localStorage for admin panel access:

### Contact Form
- **Success Message**: Shows confirmation message after submission
- **Required Fields**: Name, email, message, service selection
- **Admin Storage**: Automatically saved to admin panel for management
- **No Email Redirection**: Forms show success messages only

### Booking Form
- **Success Message**: Shows appointment confirmation message
- **Required Fields**: Name, email, preferred date/time, consultation goals
- **Admin Storage**: Automatically saved to admin panel for management
- **No Email Redirection**: Forms show success messages only

## 🚀 Deployment Options

### 1. Static Hosting (Recommended)
- **Netlify**: Drag and drop the project folder
- **Vercel**: Connect to Git repository
- **GitHub Pages**: Push to repository and enable Pages
- **AWS S3**: Upload files to S3 bucket with static hosting

### 2. Traditional Web Hosting
- Upload all files to your web server's public directory
- Ensure index.html is in the root directory
- No server-side configuration required

### 3. CDN Deployment
- Upload to any CDN service
- Configure proper MIME types for CSS and JS files

## 📋 Customization Guide

### Branding
1. **Agency Name**: Update "DigitalBoost" in HTML and CSS
2. **Colors**: Modify CSS custom properties for brand colors
3. **Logo**: Replace text logo with image in navigation
4. **Contact Info**: Update email addresses and phone numbers

### Content
1. **Services**: Modify service descriptions and pricing in HTML
2. **About Section**: Update team information and credentials
3. **Testimonials**: Replace with actual client reviews
4. **Contact Details**: Update all contact information

### Styling
1. **Colors**: Update gradient and color variables in CSS
2. **Fonts**: Change Google Fonts import and font-family declarations
3. **Animations**: Modify keyframes and transition properties
4. **Layout**: Adjust grid and flexbox properties

## 🔧 Browser Support

- **Chrome**: 60+
- **Firefox**: 60+
- **Safari**: 12+
- **Edge**: 79+
- **Mobile Browsers**: iOS Safari 12+, Chrome Mobile 60+

## 📊 Performance Features

- **Optimized Images**: CSS-based graphics and icons
- **Minimal Dependencies**: Only Google Fonts and Font Awesome
- **Efficient CSS**: Organized with minimal redundancy
- **Fast Loading**: Lightweight JavaScript with no frameworks

## 🎯 SEO Optimization

- **Semantic HTML**: Proper heading hierarchy and structure
- **Meta Tags**: Title and viewport meta tags included
- **Alt Text**: Ready for image alt text addition
- **Clean URLs**: SEO-friendly structure

## 📞 Support

For customization help or technical support:
- **Email**: <EMAIL>
- **Documentation**: This README file
- **Code Comments**: Detailed comments in all files

## 📄 License

This project is created for professional use. Customize and deploy as needed for your digital marketing agency.

---

**Built with ❤️ for digital marketing professionals**
