<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #8AA49D;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #7a9490;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>🔧 Admin Panel Test Sayfası</h1>
    
    <div class="test-section">
        <h2>📋 Test Listesi</h2>
        <div id="testResults">
            <div class="status info">Test sonuçları burada görünecek...</div>
        </div>
        
        <h3>🧪 Manuel Testler</h3>
        <button class="test-button" onclick="testLogin()">1. Login Fonksiyonu Test Et</button>
        <button class="test-button" onclick="testPasswordToggle()">2. Şifre Toggle Test Et</button>
        <button class="test-button" onclick="testConsoleErrors()">3. Console Hatalarını Kontrol Et</button>
        <button class="test-button" onclick="openAdminPanel()">4. Admin Paneli Aç</button>
    </div>

    <div class="test-section">
        <h2>🎯 Test Talimatları</h2>
        <ol>
            <li><strong>Şifre:</strong> <code>M4qsxWandoh7t.</code></li>
            <li><strong>Login Test:</strong> Şifreyi girin ve login butonuna tıklayın</li>
            <li><strong>Toggle Test:</strong> Şifre alanındaki göz ikonuna tıklayın</li>
            <li><strong>Enter Test:</strong> Şifre alanında Enter tuşuna basın</li>
            <li><strong>Hata Test:</strong> Yanlış şifre girin ve hata mesajını kontrol edin</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🖥️ Admin Panel Önizleme</h2>
        <iframe src="admin.html" id="adminFrame"></iframe>
    </div>

    <script>
        function addTestResult(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
        }

        function testLogin() {
            addTestResult('Login testi başlatılıyor...', 'info');
            
            const iframe = document.getElementById('adminFrame');
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            
            try {
                const passwordInput = iframeDoc.getElementById('password');
                const loginButton = iframeDoc.querySelector('button[type="submit"]');
                
                if (passwordInput && loginButton) {
                    passwordInput.value = 'M4qsxWandoh7t.';
                    loginButton.click();
                    
                    setTimeout(() => {
                        const dashboard = iframeDoc.getElementById('adminDashboard');
                        if (dashboard && dashboard.style.display !== 'none') {
                            addTestResult('✅ Login testi BAŞARILI - Dashboard açıldı', 'success');
                        } else {
                            addTestResult('❌ Login testi BAŞARISIZ - Dashboard açılmadı', 'error');
                        }
                    }, 1000);
                } else {
                    addTestResult('❌ Login elementleri bulunamadı', 'error');
                }
            } catch (error) {
                addTestResult(`❌ Login test hatası: ${error.message}`, 'error');
            }
        }

        function testPasswordToggle() {
            addTestResult('Şifre toggle testi başlatılıyor...', 'info');
            
            const iframe = document.getElementById('adminFrame');
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            
            try {
                const passwordInput = iframeDoc.getElementById('password');
                const toggleButton = iframeDoc.getElementById('passwordToggle');
                
                if (passwordInput && toggleButton) {
                    const initialType = passwordInput.type;
                    toggleButton.click();
                    
                    setTimeout(() => {
                        if (passwordInput.type !== initialType) {
                            addTestResult('✅ Şifre toggle testi BAŞARILI - Tip değişti', 'success');
                        } else {
                            addTestResult('❌ Şifre toggle testi BAŞARISIZ - Tip değişmedi', 'error');
                        }
                    }, 100);
                } else {
                    addTestResult('❌ Toggle elementleri bulunamadı', 'error');
                }
            } catch (error) {
                addTestResult(`❌ Toggle test hatası: ${error.message}`, 'error');
            }
        }

        function testConsoleErrors() {
            addTestResult('Console hataları kontrol ediliyor...', 'info');
            addTestResult('🔍 Tarayıcı Developer Tools > Console sekmesini açın', 'info');
            addTestResult('🔍 Kırmızı hata mesajları olup olmadığını kontrol edin', 'info');
        }

        function openAdminPanel() {
            window.open('admin.html', '_blank');
            addTestResult('🚀 Admin paneli yeni sekmede açıldı', 'info');
        }

        // Sayfa yüklendiğinde
        window.onload = function() {
            addTestResult('🎉 Test sayfası hazır! Testleri başlatabilirsiniz.', 'success');
        };
    </script>
</body>
</html>
