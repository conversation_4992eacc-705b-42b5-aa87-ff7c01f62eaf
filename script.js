// Mobile Navigation Toggle
document.addEventListener('DOMContentLoaded', function() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');

    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });

        // Close menu when clicking on a link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', () => {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
    }
});

// Smooth Scrolling for Navigation Links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            const offsetTop = target.offsetTop - 80; // Account for fixed navbar
            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });
        }
    });
});

// Navbar Background on Scroll
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 50) {
        navbar.style.background = 'rgba(255, 255, 255, 0.98)';
        navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
    } else {
        navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        navbar.style.boxShadow = 'none';
    }
});

// Contact Form Handler
document.getElementById('contactForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // Add loading state to submit button
    const submitButton = this.querySelector('button[type="submit"]');
    addLoadingState(submitButton);

    // Get form data
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);

    // Validate required fields
    if (!data.name || !data.email || !data.message) {
        const currentLang = localStorage.getItem('language') || 'en';
        const errorMsg = currentLang === 'en' ? 'Please fill in all required fields.' : 'Lütfen tüm gerekli alanları doldurun.';
        showNotification(errorMsg, 'error');
        return;
    }

    // Validate email format
    if (!isValidEmail(data.email)) {
        const currentLang = localStorage.getItem('language') || 'en';
        const errorMsg = currentLang === 'en' ? 'Please enter a valid email address.' : 'Lütfen geçerli bir e-posta adresi girin.';
        showNotification(errorMsg, 'error');
        return;
    }

    // Save to localStorage for admin panel
    saveContactSubmission(data);

    // Show success message (no email redirection)
    const currentLang = localStorage.getItem('language') || 'en';
    const successMsg = currentLang === 'en' ?
        'Message sent successfully! We\'ll get back to you within 24 hours.' :
        'Mesajınız başarıyla gönderildi! 24 saat içinde size geri döneceğiz.';
    showNotification(successMsg, 'success');

    // Reset form
    this.reset();
});

// Booking Form Handler
document.getElementById('bookingForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // Add loading state to submit button
    const submitButton = this.querySelector('button[type="submit"]');
    addLoadingState(submitButton);

    // Get form data
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);

    // Validate required fields (removed phone requirement)
    if (!data.bookingName || !data.bookingEmail || !data.preferredDate || !data.preferredTime || !data.consultationGoals) {
        const currentLang = localStorage.getItem('language') || 'en';
        const errorMsg = currentLang === 'en' ? 'Please fill in all required fields.' : 'Lütfen tüm gerekli alanları doldurun.';
        showNotification(errorMsg, 'error');
        return;
    }

    // Validate email format
    if (!isValidEmail(data.bookingEmail)) {
        const currentLang = localStorage.getItem('language') || 'en';
        const errorMsg = currentLang === 'en' ? 'Please enter a valid email address.' : 'Lütfen geçerli bir e-posta adresi girin.';
        showNotification(errorMsg, 'error');
        return;
    }

    // Validate date (must be in the future)
    const selectedDate = new Date(data.preferredDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
        const currentLang = localStorage.getItem('language') || 'en';
        const errorMsg = currentLang === 'en' ? 'Please select a future date for your consultation.' : 'Lütfen danışmanlığınız için gelecek bir tarih seçin.';
        showNotification(errorMsg, 'error');
        return;
    }

    // Save to localStorage for admin panel
    saveBookingSubmission(data);

    // Show success message (no email redirection)
    const currentLang = localStorage.getItem('language') || 'en';
    const successMsg = currentLang === 'en' ?
        'Appointment request created successfully! We\'ll confirm your appointment within 24 hours.' :
        'Randevu talebiniz başarıyla oluşturuldu! 24 saat içinde randevunuzu onaylayacağız.';
    showNotification(successMsg, 'success');

    // Reset form
    this.reset();
});

// Email validation function
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Notification system
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        z-index: 10000;
        max-width: 400px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        animation: slideIn 0.3s ease-out;
    `;
    
    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        .notification-content {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .notification-close {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            margin-left: auto;
            padding: 0.25rem;
        }
    `;
    document.head.appendChild(style);
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Set minimum date for booking form to today
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('preferredDate');
    if (dateInput) {
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        dateInput.min = tomorrow.toISOString().split('T')[0];
    }
});

// Add loading states to buttons
function addLoadingState(button) {
    const currentLang = localStorage.getItem('language') || 'en';
    const loadingText = currentLang === 'en' ? 'Sending...' : 'Gönderiliyor...';

    const originalText = button.innerHTML;
    button.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${loadingText}`;
    button.disabled = true;

    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 2000);
}



// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for animation
document.addEventListener('DOMContentLoaded', function() {
    const animatedElements = document.querySelectorAll('.service-card, .testimonial-card, .credential, .stat');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});

// Language Switching Functionality
class LanguageSwitcher {
    constructor() {
        this.currentLanguage = this.detectBrowserLanguage();
        this.init();
    }

    detectBrowserLanguage() {
        // Check if language is already stored
        const storedLang = localStorage.getItem('language');
        if (storedLang) {
            return storedLang;
        }

        // Detect browser language
        const browserLang = navigator.language || navigator.languages[0];
        const langCode = browserLang.toLowerCase().split('-')[0];

        // Return 'tr' for Turkish, 'en' for everything else
        return langCode === 'tr' ? 'tr' : 'en';
    }

    init() {
        // Set initial language silently (no notification)
        this.setLanguage(this.currentLanguage, true);

        // Add event listeners to language buttons
        document.addEventListener('DOMContentLoaded', () => {
            const langButtons = document.querySelectorAll('.lang-btn');
            langButtons.forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const lang = e.currentTarget.getAttribute('data-lang');
                    this.setLanguage(lang, false); // Show notification on manual change
                });
            });
        });
    }

    setLanguage(lang, silent = false) {
        this.currentLanguage = lang;
        localStorage.setItem('language', lang);

        // Update HTML lang attribute
        const htmlRoot = document.getElementById('html-root') || document.documentElement;
        htmlRoot.setAttribute('lang', lang);

        // Update all elements with data attributes
        const elements = document.querySelectorAll('[data-en][data-tr]');
        elements.forEach(element => {
            const text = element.getAttribute(`data-${lang}`);
            if (text) {
                if (element.tagName === 'TITLE') {
                    element.textContent = text;
                } else {
                    element.textContent = text;
                }
            }
        });

        // Update placeholders
        const placeholderElements = document.querySelectorAll(`[data-${lang}-placeholder]`);
        placeholderElements.forEach(element => {
            const placeholder = element.getAttribute(`data-${lang}-placeholder`);
            if (placeholder) {
                element.placeholder = placeholder;
            }
        });

        // Update language buttons
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-lang') === lang) {
                btn.classList.add('active');
            }
        });

        // Update form placeholders and labels based on language
        this.updateFormTexts(lang);

        // Show language change notification only if not silent
        if (!silent) {
            const langName = lang === 'en' ? 'English' : 'Türkçe';
            showNotification(`${lang === 'en' ? 'Language changed to' : 'Dil değiştirildi:'} ${langName}`, 'success');
        }
    }

    updateFormTexts(lang) {
        // Update form placeholders and static text
        const formTexts = {
            en: {
                'message': 'Tell us about your project, goals, and how we can help...',
                'consultationGoals': 'Briefly describe your current challenges and what you hope to achieve...',
                'bookingNote': 'We\'ll confirm your appointment within 24 hours and send you a calendar invite.'
            },
            tr: {
                'message': 'Projeniz, hedefleriniz ve size nasıl yardımcı olabileceğimiz hakkında bize bilgi verin...',
                'consultationGoals': 'Mevcut zorluklarınızı ve elde etmeyi umduğunuz sonuçları kısaca açıklayın...',
                'bookingNote': 'Randevunuzu 24 saat içinde onaylayacağız ve size takvim daveti göndereceğiz.'
            }
        };

        // Update textarea placeholders
        const messageTextarea = document.getElementById('message');
        const consultationTextarea = document.getElementById('consultationGoals');
        const bookingNote = document.querySelector('.booking-note');

        if (messageTextarea && formTexts[lang]['message']) {
            messageTextarea.placeholder = formTexts[lang]['message'];
        }

        if (consultationTextarea && formTexts[lang]['consultationGoals']) {
            consultationTextarea.placeholder = formTexts[lang]['consultationGoals'];
        }

        if (bookingNote && formTexts[lang]['bookingNote']) {
            const icon = bookingNote.querySelector('i');
            bookingNote.innerHTML = '';
            bookingNote.appendChild(icon);
            bookingNote.appendChild(document.createTextNode(' ' + formTexts[lang]['bookingNote']));
        }
    }
}

// Initialize language switcher
const languageSwitcher = new LanguageSwitcher();

// Data Storage Functions for Admin Panel
function saveContactSubmission(data) {
    const submissions = JSON.parse(localStorage.getItem('contactSubmissions') || '[]');
    const submission = {
        id: generateId(),
        type: 'contact',
        timestamp: new Date().toISOString(),
        status: 'unread',
        data: {
            name: data.name,
            email: data.email,
            service: data.service || '',
            budget: data.budget || '',
            message: data.message
        }
    };

    submissions.unshift(submission); // Add to beginning of array
    localStorage.setItem('contactSubmissions', JSON.stringify(submissions));

    // Update admin notification counter
    updateAdminNotificationCount();
}

function saveBookingSubmission(data) {
    const bookings = JSON.parse(localStorage.getItem('bookingSubmissions') || '[]');
    const booking = {
        id: generateId(),
        type: 'booking',
        timestamp: new Date().toISOString(),
        status: 'unread',
        data: {
            name: data.bookingName,
            email: data.bookingEmail,
            preferredDate: data.preferredDate,
            preferredTime: data.preferredTime,
            consultationGoals: data.consultationGoals
        }
    };

    bookings.unshift(booking); // Add to beginning of array
    localStorage.setItem('bookingSubmissions', JSON.stringify(bookings));

    // Update admin notification counter
    updateAdminNotificationCount();
}

function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function updateAdminNotificationCount() {
    const contacts = JSON.parse(localStorage.getItem('contactSubmissions') || '[]');
    const bookings = JSON.parse(localStorage.getItem('bookingSubmissions') || '[]');
    const unreadCount = contacts.filter(item => item.status === 'unread').length +
                       bookings.filter(item => item.status === 'unread').length;

    localStorage.setItem('adminNotificationCount', unreadCount.toString());
}

// Admin Panel Data Access Functions
function getAllContactSubmissions() {
    return JSON.parse(localStorage.getItem('contactSubmissions') || '[]');
}

function getAllBookingSubmissions() {
    return JSON.parse(localStorage.getItem('bookingSubmissions') || '[]');
}

function updateSubmissionStatus(id, status, type) {
    const storageKey = type === 'contact' ? 'contactSubmissions' : 'bookingSubmissions';
    const submissions = JSON.parse(localStorage.getItem(storageKey) || '[]');
    const index = submissions.findIndex(item => item.id === id);

    if (index !== -1) {
        submissions[index].status = status;
        localStorage.setItem(storageKey, JSON.stringify(submissions));
        updateAdminNotificationCount();
        return true;
    }
    return false;
}

function deleteSubmission(id, type) {
    const storageKey = type === 'contact' ? 'contactSubmissions' : 'bookingSubmissions';
    const submissions = JSON.parse(localStorage.getItem(storageKey) || '[]');
    const filteredSubmissions = submissions.filter(item => item.id !== id);

    localStorage.setItem(storageKey, JSON.stringify(filteredSubmissions));
    updateAdminNotificationCount();
    return true;
}

function exportSubmissionsData() {
    const contacts = getAllContactSubmissions();
    const bookings = getAllBookingSubmissions();

    const exportData = {
        exportDate: new Date().toISOString(),
        contactSubmissions: contacts,
        bookingSubmissions: bookings,
        totalContacts: contacts.length,
        totalBookings: bookings.length
    };

    return JSON.stringify(exportData, null, 2);
}
